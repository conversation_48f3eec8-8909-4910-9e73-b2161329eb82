#!/usr/bin/env python3
"""
Quick demonstration of the TFT Well Log Test Suite functionality.

This script provides a simple demonstration of loading LAS files and 
running a basic test without the full comprehensive test suite.
"""

import os
import warnings
import numpy as np

# Suppress warnings for cleaner output
warnings.filterwarnings('ignore')
os.environ['TF_CPP_MIN_LOG_LEVEL'] = '2'

def demo_las_loading():
    """Demonstrate LAS file loading capabilities."""
    print("="*60)
    print("DEMO: LAS FILE LOADING")
    print("="*60)
    
    from las_utils import LASDataLoader
    
    # Initialize loader
    loader = LASDataLoader("Las data")
    
    # Get available files
    las_files = loader.get_las_files()
    print(f"Available LAS files: {len(las_files)}")
    for i, file in enumerate(las_files):
        print(f"  {i+1}. {os.path.basename(file)}")
    
    if not las_files:
        print("No LAS files found. Please check the 'Las data' directory.")
        return None
    
    # Load first file as example
    print(f"\nLoading example file: {os.path.basename(las_files[0])}")
    df = loader.load_single_las(las_files[0])
    
    if df is not None:
        print(f"Successfully loaded {len(df)} samples")
        print(f"Columns: {list(df.columns)}")
        print(f"Well ID: {df['well_id'].iloc[0]}")
        print(f"Depth range: {df['depth'].min():.1f} - {df['depth'].max():.1f} m")
        
        # Show data quality
        print("\nData quality summary:")
        for col in ['gamma_ray', 'resistivity', 'density', 'neutron', 'p_wave']:
            if col in df.columns:
                valid_count = df[col].notna().sum()
                total_count = len(df)
                completeness = (valid_count / total_count) * 100
                print(f"  {col}: {completeness:.1f}% complete")
        
        return df
    else:
        print("Failed to load LAS file")
        return None

def demo_data_formatter(df):
    """Demonstrate data formatter functionality."""
    if df is None:
        return
    
    print("\n" + "="*60)
    print("DEMO: DATA FORMATTER")
    print("="*60)
    
    from data_formatters.welllog import WellLogFormatter
    
    # Initialize formatter
    formatter = WellLogFormatter()
    
    # Show column definitions
    print("Column definitions:")
    for col_name, data_type, input_type in formatter.get_column_definition():
        print(f"  {col_name}: {data_type.name} ({input_type.name})")
    
    # Show model parameters
    print("\nFixed parameters:")
    fixed_params = formatter.get_fixed_params()
    for key, value in fixed_params.items():
        print(f"  {key}: {value}")
    
    print("\nModel parameters:")
    model_params = formatter.get_default_model_params()
    for key, value in model_params.items():
        print(f"  {key}: {value}")

def demo_single_file_scenario():
    """Demonstrate single-file scenario setup."""
    print("\n" + "="*60)
    print("DEMO: SINGLE-FILE SCENARIO SETUP")
    print("="*60)
    
    try:
        from test_scenarios import SingleFileScenario
        
        # Initialize scenario
        scenario = SingleFileScenario("Las data")
        print("Single-file scenario initialized successfully")
        
        # Show what would happen (without actually training)
        print("\nThis scenario would:")
        print("1. Load a single LAS file")
        print("2. Split data by depth (70% train, 15% valid, 15% test)")
        print("3. Train TFT model on training data")
        print("4. Make predictions on test data")
        print("5. Evaluate performance and create visualizations")
        
    except Exception as e:
        print(f"Error initializing single-file scenario: {e}")

def demo_multi_file_scenario():
    """Demonstrate multi-file scenario setup."""
    print("\n" + "="*60)
    print("DEMO: MULTI-FILE SCENARIO SETUP")
    print("="*60)
    
    try:
        from test_scenarios import MultiFileScenario
        from las_utils import LASDataLoader
        
        # Check if we have enough files
        loader = LASDataLoader("Las data")
        las_files = loader.get_las_files()
        
        if len(las_files) < 2:
            print("Multi-file scenario requires at least 2 LAS files")
            print(f"Currently have {len(las_files)} files")
            return
        
        # Initialize scenario
        scenario = MultiFileScenario("Las data")
        print("Multi-file scenario initialized successfully")
        
        # Show file split
        train_files, pred_files = loader.split_files_for_scenarios(0.7)
        print(f"\nFile split (70/30):")
        print(f"Training files ({len(train_files)}):")
        for f in train_files:
            print(f"  - {os.path.basename(f)}")
        print(f"Prediction files ({len(pred_files)}):")
        for f in pred_files:
            print(f"  - {os.path.basename(f)}")
        
        print("\nThis scenario would:")
        print("1. Load multiple LAS files")
        print("2. Split files into training and prediction sets")
        print("3. Train TFT model on training wells")
        print("4. Make predictions on separate prediction wells")
        print("5. Evaluate cross-well performance")
        
    except Exception as e:
        print(f"Error initializing multi-file scenario: {e}")

def main():
    """Run the demonstration."""
    print("TFT WELL LOG TEST SUITE - QUICK DEMONSTRATION")
    print("This demo shows the capabilities without running full training")
    print()
    
    # Demo 1: LAS file loading
    df = demo_las_loading()
    
    # Demo 2: Data formatter
    demo_data_formatter(df)
    
    # Demo 3: Single-file scenario
    demo_single_file_scenario()
    
    # Demo 4: Multi-file scenario
    demo_multi_file_scenario()
    
    print("\n" + "="*60)
    print("DEMO COMPLETED")
    print("="*60)
    print("To run the full test suite, use:")
    print("  python test_tft_welllog_comprehensive.py")
    print()
    print("For help with options:")
    print("  python test_tft_welllog_comprehensive.py --help")

if __name__ == "__main__":
    main()

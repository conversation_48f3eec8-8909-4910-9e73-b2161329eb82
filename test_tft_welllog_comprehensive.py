#!/usr/bin/env python3
"""
Comprehensive Test Suite for Temporal Fusion Transformer (TFT) with Well Log Data

This script demonstrates training and prediction using the TFT implementation with 
LAS (Log ASCII Standard) files. It implements two distinct testing scenarios:

1. Single-file scenario: Training and prediction on the same LAS file (split by depth)
2. Multi-file scenario: Training and prediction on different LAS files

The test predicts P-wave velocity using gamma ray, resistivity, density, and neutron 
as input features.

Author: TFT Well Log Test Suite
Date: 2025
"""

import os
import sys
import argparse
import traceback
import warnings
from datetime import datetime
from typing import Dict, List, Optional

# Suppress TensorFlow warnings for cleaner output
os.environ['TF_CPP_MIN_LOG_LEVEL'] = '2'
warnings.filterwarnings('ignore', category=FutureWarning)
warnings.filterwarnings('ignore', category=UserWarning)

# Import TensorFlow with v1 compatibility
import tensorflow.compat.v1 as tf
tf.compat.v1.disable_v2_behavior()

# Import our test scenarios
from test_scenarios import SingleFileScenario, MultiFileScenario
from las_utils import LASDataLoader

# Import data formatter
import data_formatters.welllog
import expt_settings.configs


class TFTWellLogTester:
    """
    Comprehensive tester for TFT with well log data.
    
    This class orchestrates both single-file and multi-file testing scenarios,
    providing comprehensive evaluation of the TFT model's performance on well log data.
    """
    
    def __init__(self, las_directory: str = "Las data", output_dir: str = "test_results"):
        """
        Initialize the comprehensive tester.
        
        Args:
            las_directory: Directory containing LAS files
            output_dir: Directory to save test results and outputs
        """
        self.las_directory = las_directory
        self.output_dir = output_dir
        self.loader = LASDataLoader(las_directory)
        
        # Create output directory if it doesn't exist
        os.makedirs(output_dir, exist_ok=True)
        
        # Initialize test results storage
        self.test_results = {}
        
    def run_comprehensive_test(self, scenarios: List[str] = None, 
                             target_file: Optional[str] = None) -> Dict:
        """
        Run comprehensive testing of both scenarios.
        
        Args:
            scenarios: List of scenarios to run ['single', 'multi'] or None for both
            target_file: Specific LAS file for single-file scenario
            
        Returns:
            Dictionary containing all test results
        """
        print("="*80)
        print("TFT WELL LOG COMPREHENSIVE TEST SUITE")
        print("="*80)
        print(f"Test started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"LAS directory: {self.las_directory}")
        print(f"Output directory: {self.output_dir}")
        print()
        
        # Default to running both scenarios
        if scenarios is None:
            scenarios = ['single', 'multi']
        
        # Validate LAS files availability
        self._validate_setup()
        
        # Run requested scenarios
        for scenario in scenarios:
            try:
                if scenario == 'single':
                    self._run_single_file_scenario(target_file)
                elif scenario == 'multi':
                    self._run_multi_file_scenario()
                else:
                    print(f"Warning: Unknown scenario '{scenario}' skipped")
                    
            except Exception as e:
                print(f"Error in {scenario} scenario: {str(e)}")
                print("Traceback:")
                traceback.print_exc()
                self.test_results[f'{scenario}_error'] = str(e)
        
        # Generate comprehensive report
        self._generate_comprehensive_report()
        
        print(f"\nTest completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("="*80)
        
        return self.test_results
    
    def _validate_setup(self):
        """Validate that the test setup is correct."""
        print("Validating test setup...")
        
        # Check LAS directory exists
        if not os.path.exists(self.las_directory):
            raise ValueError(f"LAS directory not found: {self.las_directory}")
        
        # Check for LAS files
        las_files = self.loader.get_las_files()
        if not las_files:
            raise ValueError(f"No LAS files found in {self.las_directory}")
        
        print(f"✓ Found {len(las_files)} LAS files")
        
        # Check if we have enough files for multi-file scenario
        if len(las_files) < 2:
            print("⚠ Warning: Only 1 LAS file found. Multi-file scenario will be skipped.")
        
        # Test loading one file to validate format
        try:
            test_df = self.loader.load_single_las(las_files[0])
            if test_df is None or len(test_df) == 0:
                raise ValueError("Failed to load test LAS file")
            print(f"✓ Successfully validated LAS file format")
            
            # Check for required curves
            required_curves = ['p_wave', 'gamma_ray', 'resistivity', 'density', 'neutron']
            missing_curves = [curve for curve in required_curves if curve not in test_df.columns]
            if missing_curves:
                print(f"⚠ Warning: Missing curves in test file: {missing_curves}")
            else:
                print("✓ All required curves found")
                
        except Exception as e:
            raise ValueError(f"Failed to validate LAS file format: {str(e)}")
        
        print("Setup validation completed successfully.\n")
    
    def _run_single_file_scenario(self, target_file: Optional[str] = None):
        """Run the single-file scenario."""
        print("Starting Single-File Scenario...")
        
        try:
            scenario = SingleFileScenario(self.las_directory)
            results = scenario.run_scenario(target_file)
            self.test_results['single_file'] = results
            
            # Save detailed results
            self._save_scenario_results('single_file', results)
            
        except Exception as e:
            print(f"Single-file scenario failed: {str(e)}")
            raise
    
    def _run_multi_file_scenario(self):
        """Run the multi-file scenario."""
        print("Starting Multi-File Scenario...")
        
        # Check if we have enough files
        las_files = self.loader.get_las_files()
        if len(las_files) < 2:
            print("Skipping multi-file scenario: Need at least 2 LAS files")
            return
        
        try:
            scenario = MultiFileScenario(self.las_directory)
            results = scenario.run_scenario()
            self.test_results['multi_file'] = results
            
            # Save detailed results
            self._save_scenario_results('multi_file', results)
            
        except Exception as e:
            print(f"Multi-file scenario failed: {str(e)}")
            raise
    
    def _save_scenario_results(self, scenario_name: str, results: Dict):
        """Save detailed results for a scenario."""
        import json
        
        # Create a JSON-serializable version of results
        serializable_results = self._make_serializable(results)
        
        # Save to JSON file
        results_file = os.path.join(self.output_dir, f"{scenario_name}_results.json")
        with open(results_file, 'w') as f:
            json.dump(serializable_results, f, indent=2)
        
        print(f"Detailed results saved to: {results_file}")
    
    def _make_serializable(self, obj):
        """Convert objects to JSON-serializable format."""
        import pandas as pd
        import numpy as np
        
        if isinstance(obj, dict):
            return {k: self._make_serializable(v) for k, v in obj.items()}
        elif isinstance(obj, list):
            return [self._make_serializable(item) for item in obj]
        elif isinstance(obj, pd.DataFrame):
            return obj.to_dict('records')
        elif isinstance(obj, np.ndarray):
            return obj.tolist()
        elif isinstance(obj, (np.integer, np.floating)):
            return obj.item()
        elif pd.isna(obj):
            return None
        else:
            return obj
    
    def _generate_comprehensive_report(self):
        """Generate a comprehensive report of all test results."""
        print("\n" + "="*80)
        print("COMPREHENSIVE TEST RESULTS SUMMARY")
        print("="*80)
        
        if 'single_file' in self.test_results:
            self._print_scenario_summary('Single-File', self.test_results['single_file'])
        
        if 'multi_file' in self.test_results:
            self._print_scenario_summary('Multi-File', self.test_results['multi_file'])
        
        # Compare scenarios if both were run
        if 'single_file' in self.test_results and 'multi_file' in self.test_results:
            self._compare_scenarios()
        
        # Save comprehensive report
        self._save_comprehensive_report()
    
    def _print_scenario_summary(self, scenario_name: str, results: Dict):
        """Print summary for a specific scenario."""
        print(f"\n{scenario_name} Scenario Summary:")
        print("-" * 40)
        
        if 'metrics' in results:
            metrics = results['metrics']
            print(f"  MAE: {metrics.get('mae', 'N/A'):.3f}")
            print(f"  RMSE: {metrics.get('rmse', 'N/A'):.3f}")
            print(f"  P50 Loss: {metrics.get('p50_loss', 'N/A'):.4f}")
        
        if 'data_summary' in results:
            data_summary = results['data_summary']
            if scenario_name == 'Single-File':
                print(f"  Total samples: {data_summary.get('total_samples', 'N/A')}")
                print(f"  Well: {data_summary.get('well_id', 'N/A')}")
            else:
                print(f"  Training wells: {data_summary.get('train_wells', 'N/A')}")
                print(f"  Prediction wells: {data_summary.get('pred_wells', 'N/A')}")
    
    def _compare_scenarios(self):
        """Compare results between scenarios."""
        print(f"\nScenario Comparison:")
        print("-" * 40)
        
        single_metrics = self.test_results['single_file'].get('metrics', {})
        multi_metrics = self.test_results['multi_file'].get('metrics', {})
        
        for metric in ['mae', 'rmse', 'p50_loss']:
            single_val = single_metrics.get(metric, 0)
            multi_val = multi_metrics.get(metric, 0)
            
            if single_val > 0 and multi_val > 0:
                diff_pct = ((multi_val - single_val) / single_val) * 100
                print(f"  {metric.upper()}: Single={single_val:.3f}, Multi={multi_val:.3f} ({diff_pct:+.1f}%)")
    
    def _save_comprehensive_report(self):
        """Save comprehensive report to file."""
        report_file = os.path.join(self.output_dir, "comprehensive_report.txt")
        
        with open(report_file, 'w') as f:
            f.write("TFT WELL LOG COMPREHENSIVE TEST REPORT\n")
            f.write("="*50 + "\n")
            f.write(f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
            
            # Write detailed results for each scenario
            for scenario_key, results in self.test_results.items():
                if isinstance(results, dict) and 'metrics' in results:
                    f.write(f"{scenario_key.upper()} SCENARIO:\n")
                    f.write("-" * 30 + "\n")
                    
                    metrics = results['metrics']
                    for metric, value in metrics.items():
                        f.write(f"{metric}: {value}\n")
                    f.write("\n")
        
        print(f"Comprehensive report saved to: {report_file}")


def main():
    """Main function to run the comprehensive test suite."""
    parser = argparse.ArgumentParser(description='TFT Well Log Comprehensive Test Suite')
    parser.add_argument('--las-dir', default='Las data', 
                       help='Directory containing LAS files (default: Las data)')
    parser.add_argument('--output-dir', default='test_results',
                       help='Output directory for results (default: test_results)')
    parser.add_argument('--scenarios', nargs='+', choices=['single', 'multi'],
                       help='Scenarios to run (default: both)')
    parser.add_argument('--target-file', 
                       help='Specific LAS file for single-file scenario')
    parser.add_argument('--verbose', action='store_true',
                       help='Enable verbose output')
    
    args = parser.parse_args()
    
    # Set TensorFlow logging level
    if not args.verbose:
        tf.logging.set_verbosity(tf.logging.ERROR)
    
    try:
        # Initialize and run comprehensive test
        tester = TFTWellLogTester(args.las_dir, args.output_dir)
        results = tester.run_comprehensive_test(args.scenarios, args.target_file)
        
        print("\n✓ Comprehensive test completed successfully!")
        return 0
        
    except Exception as e:
        print(f"\n✗ Test failed: {str(e)}")
        if args.verbose:
            traceback.print_exc()
        return 1


if __name__ == "__main__":
    sys.exit(main())

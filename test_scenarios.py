"""
Test scenarios for Temporal Fusion Transformer (TFT) with well log data.

This module implements two distinct testing scenarios:
1. Single-file scenario: Training and prediction on the same LAS file (split by depth)
2. Multi-file scenario: Training and prediction on different LAS files
"""

import os
import pandas as pd
import numpy as np
import tensorflow.compat.v1 as tf
from typing import Dict, List, Tuple, Optional
import matplotlib.pyplot as plt
import seaborn as sns

# Import TFT components
import data_formatters.welllog
import libs.tft_model
import libs.utils as utils

# Import our LAS utilities
from las_utils import LASDataLoader

# Use TensorFlow 2.x but with v1 compatibility for the TFT model
tf.compat.v1.disable_v2_behavior()

WellLogFormatter = data_formatters.welllog.WellLogFormatter
ModelClass = libs.tft_model.TemporalFusionTransformer


class SingleFileScenario:
    """
    Test scenario where training and prediction are performed on the same LAS file,
    split by depth intervals (temporal/depth-based splitting).
    """
    
    def __init__(self, las_directory: str = "Las data", train_ratio: float = 0.7):
        """
        Initialize single-file scenario.
        
        Args:
            las_directory: Directory containing LAS files
            train_ratio: Fraction of depth range to use for training
        """
        self.las_directory = las_directory
        self.train_ratio = train_ratio
        self.loader = LASDataLoader(las_directory)
        self.formatter = WellLogFormatter()
        
    def run_scenario(self, target_file: Optional[str] = None) -> Dict:
        """
        Run the single-file scenario.
        
        Args:
            target_file: Specific LAS file to use, or None to use the first available
            
        Returns:
            Dictionary containing results and metrics
        """
        print("="*60)
        print("SINGLE-FILE SCENARIO")
        print("="*60)
        print("Training and prediction on the same LAS file, split by depth intervals")
        print()
        
        # Load data from a single file
        las_files = self.loader.get_las_files()
        if not las_files:
            raise ValueError(f"No LAS files found in {self.las_directory}")
        
        # Select target file
        if target_file is None:
            target_file = las_files[0]  # Use first file
        elif target_file not in las_files:
            raise ValueError(f"Target file {target_file} not found")
        
        print(f"Using LAS file: {os.path.basename(target_file)}")
        
        # Load single file
        df = self.loader.load_single_las(target_file)
        if df is None or len(df) == 0:
            raise ValueError(f"Could not load data from {target_file}")
        
        print(f"Loaded {len(df)} samples from {df['well_id'].iloc[0]}")
        
        # Split by depth within the well
        df_split = self._split_by_depth(df)
        
        # Prepare data for TFT
        train_data, valid_data, test_data = self._prepare_tft_data(df_split)
        
        # Train model
        model, training_results = self._train_model(train_data, valid_data)
        
        # Make predictions
        predictions = self._make_predictions(model, test_data)
        
        # Evaluate results
        metrics = self._evaluate_predictions(predictions, test_data)
        
        # Create visualizations
        self._create_visualizations(df_split, predictions, target_file)
        
        results = {
            'scenario': 'single_file',
            'file_used': target_file,
            'data_summary': self._get_data_summary(df_split),
            'training_results': training_results,
            'predictions': predictions,
            'metrics': metrics
        }
        
        self._print_results_summary(results)
        
        return results
    
    def _split_by_depth(self, df: pd.DataFrame) -> pd.DataFrame:
        """Split data by depth intervals within the well."""
        print(f"\nSplitting data by depth (train ratio: {self.train_ratio})")
        
        # Sort by depth
        df_sorted = df.sort_values('depth').reset_index(drop=True)
        
        # Calculate split points
        n_samples = len(df_sorted)
        train_end = int(n_samples * self.train_ratio)
        valid_end = int(n_samples * 0.85)  # 70% train, 15% valid, 15% test
        
        # Add split labels
        df_sorted['split'] = 'test'
        df_sorted.loc[:train_end-1, 'split'] = 'train'
        df_sorted.loc[train_end:valid_end-1, 'split'] = 'valid'
        
        print(f"Train samples: {sum(df_sorted['split'] == 'train')}")
        print(f"Valid samples: {sum(df_sorted['split'] == 'valid')}")
        print(f"Test samples: {sum(df_sorted['split'] == 'test')}")
        
        return df_sorted
    
    def _prepare_tft_data(self, df: pd.DataFrame) -> Tuple[pd.DataFrame, pd.DataFrame, pd.DataFrame]:
        """Prepare data for TFT training."""
        print("\nPreparing data for TFT...")
        
        # Remove split column for TFT processing
        df_clean = df.drop('split', axis=1)
        
        # Use the formatter's split method, but we'll override with our custom splits
        train_df = df_clean[df['split'] == 'train'].copy()
        valid_df = df_clean[df['split'] == 'valid'].copy()
        test_df = df_clean[df['split'] == 'test'].copy()
        
        # Set scalers using training data
        self.formatter.set_scalers(train_df)
        
        # Transform all datasets
        train_transformed = self.formatter.transform_inputs(train_df)
        valid_transformed = self.formatter.transform_inputs(valid_df)
        test_transformed = self.formatter.transform_inputs(test_df)
        
        return train_transformed, valid_transformed, test_transformed
    
    def _train_model(self, train_data: pd.DataFrame, valid_data: pd.DataFrame) -> Tuple[ModelClass, Dict]:
        """Train the TFT model."""
        print("\nTraining TFT model...")
        
        # Get model parameters
        fixed_params = self.formatter.get_fixed_params()
        model_params = self.formatter.get_default_model_params()
        
        # Reduce parameters for testing
        fixed_params['num_epochs'] = 10  # Reduced for testing
        fixed_params['total_time_steps'] = 20
        fixed_params['num_encoder_steps'] = 15
        model_params['hidden_layer_size'] = 32
        model_params['minibatch_size'] = 16
        
        # Combine parameters
        all_params = {**fixed_params, **model_params}
        all_params['column_definition'] = self.formatter.get_column_definition()
        all_params['model_folder'] = 'temp_model'
        
        # Create model
        model = ModelClass(all_params, use_cudnn=False)
        
        # Cache data
        train_samples, valid_samples = 500, 100  # Reduced for testing
        model.cache_batched_data(train_data, "train", num_samples=train_samples)
        model.cache_batched_data(valid_data, "valid", num_samples=valid_samples)
        
        # Train model
        model.fit()
        
        training_results = {
            'epochs_completed': fixed_params['num_epochs'],
            'train_samples': len(train_data),
            'valid_samples': len(valid_data)
        }
        
        return model, training_results
    
    def _make_predictions(self, model: ModelClass, test_data: pd.DataFrame) -> Dict:
        """Make predictions using the trained model."""
        print("\nMaking predictions...")
        
        # Get predictions
        output_map = model.predict(test_data, return_targets=True)
        
        # Format predictions
        targets = self.formatter.format_predictions(output_map["targets"])
        p50_forecast = self.formatter.format_predictions(output_map["p50"])
        p90_forecast = self.formatter.format_predictions(output_map["p90"])
        p10_forecast = self.formatter.format_predictions(output_map["p10"])
        
        predictions = {
            'targets': targets,
            'p50': p50_forecast,
            'p90': p90_forecast,
            'p10': p10_forecast,
            'test_samples': len(test_data)
        }
        
        return predictions
    
    def _evaluate_predictions(self, predictions: Dict, test_data: pd.DataFrame) -> Dict:
        """Evaluate prediction quality."""
        print("\nEvaluating predictions...")
        
        targets = predictions['targets']
        p50_forecast = predictions['p50']
        
        # Extract numerical data (remove metadata columns)
        def extract_numerical_data(data):
            return data[[col for col in data.columns 
                        if col not in {"forecast_time", "identifier"}]]
        
        targets_num = extract_numerical_data(targets)
        p50_num = extract_numerical_data(p50_forecast)
        
        # Calculate metrics
        mae = np.mean(np.abs(targets_num.values - p50_num.values))
        rmse = np.sqrt(np.mean((targets_num.values - p50_num.values) ** 2))
        
        # Calculate normalized quantile loss
        p50_loss = utils.numpy_normalised_quantile_loss(
            targets_num.values, p50_num.values, 0.5)
        
        metrics = {
            'mae': mae,
            'rmse': rmse,
            'p50_loss': p50_loss,
            'target_mean': np.mean(targets_num.values),
            'target_std': np.std(targets_num.values)
        }
        
        return metrics
    
    def _create_visualizations(self, df: pd.DataFrame, predictions: Dict, target_file: str):
        """Create visualization plots."""
        print("\nCreating visualizations...")
        
        try:
            # Create figure with subplots
            fig, axes = plt.subplots(2, 2, figsize=(15, 10))
            fig.suptitle(f'Single-File Scenario Results: {os.path.basename(target_file)}', fontsize=16)
            
            # Plot 1: Data distribution by split
            split_counts = df['split'].value_counts()
            axes[0, 0].bar(split_counts.index, split_counts.values)
            axes[0, 0].set_title('Data Distribution by Split')
            axes[0, 0].set_ylabel('Number of Samples')
            
            # Plot 2: P-wave velocity vs depth
            for split in ['train', 'valid', 'test']:
                split_data = df[df['split'] == split]
                axes[0, 1].scatter(split_data['p_wave'], split_data['depth'], 
                                 label=split, alpha=0.6, s=10)
            axes[0, 1].set_xlabel('P-wave Velocity (m/s)')
            axes[0, 1].set_ylabel('Depth (m)')
            axes[0, 1].set_title('P-wave Velocity vs Depth')
            axes[0, 1].legend()
            axes[0, 1].invert_yaxis()  # Invert y-axis for depth
            
            # Plot 3: Prediction vs actual (if we have predictions)
            if predictions and len(predictions['targets']) > 0:
                targets = predictions['targets'].iloc[:, 2:].values.flatten()  # Skip metadata columns
                p50_pred = predictions['p50'].iloc[:, 2:].values.flatten()
                
                axes[1, 0].scatter(targets, p50_pred, alpha=0.6, s=10)
                axes[1, 0].plot([targets.min(), targets.max()], 
                               [targets.min(), targets.max()], 'r--', lw=2)
                axes[1, 0].set_xlabel('Actual P-wave Velocity')
                axes[1, 0].set_ylabel('Predicted P-wave Velocity')
                axes[1, 0].set_title('Predictions vs Actual')
            
            # Plot 4: Input features correlation
            feature_cols = ['gamma_ray', 'resistivity', 'density', 'neutron']
            available_cols = [col for col in feature_cols if col in df.columns]
            if len(available_cols) > 1:
                corr_matrix = df[available_cols + ['p_wave']].corr()
                sns.heatmap(corr_matrix, annot=True, cmap='coolwarm', center=0, ax=axes[1, 1])
                axes[1, 1].set_title('Feature Correlation Matrix')
            
            plt.tight_layout()
            
            # Save plot
            plot_filename = f"single_file_results_{os.path.splitext(os.path.basename(target_file))[0]}.png"
            plt.savefig(plot_filename, dpi=150, bbox_inches='tight')
            print(f"Visualization saved as: {plot_filename}")
            
            plt.show()
            
        except Exception as e:
            print(f"Error creating visualizations: {str(e)}")
    
    def _get_data_summary(self, df: pd.DataFrame) -> Dict:
        """Get summary statistics for the data."""
        return {
            'total_samples': len(df),
            'depth_range': (df['depth'].min(), df['depth'].max()),
            'well_id': df['well_id'].iloc[0],
            'train_samples': sum(df['split'] == 'train'),
            'valid_samples': sum(df['split'] == 'valid'),
            'test_samples': sum(df['split'] == 'test')
        }
    
    def _print_results_summary(self, results: Dict):
        """Print a summary of the results."""
        print("\n" + "="*60)
        print("SINGLE-FILE SCENARIO RESULTS SUMMARY")
        print("="*60)
        
        data_summary = results['data_summary']
        metrics = results['metrics']
        
        print(f"File: {os.path.basename(results['file_used'])}")
        print(f"Well: {data_summary['well_id']}")
        print(f"Total samples: {data_summary['total_samples']}")
        print(f"Depth range: {data_summary['depth_range'][0]:.1f} - {data_summary['depth_range'][1]:.1f} m")
        print()
        print("Data splits:")
        print(f"  Training: {data_summary['train_samples']} samples")
        print(f"  Validation: {data_summary['valid_samples']} samples")
        print(f"  Test: {data_summary['test_samples']} samples")
        print()
        print("Prediction metrics:")
        print(f"  MAE: {metrics['mae']:.2f}")
        print(f"  RMSE: {metrics['rmse']:.2f}")
        print(f"  P50 Loss: {metrics['p50_loss']:.4f}")
        print(f"  Target mean: {metrics['target_mean']:.2f}")
        print(f"  Target std: {metrics['target_std']:.2f}")
        print("="*60)


class MultiFileScenario:
    """
    Test scenario where different LAS files are used for training vs prediction.
    Some wells are used for training, others for prediction.
    """

    def __init__(self, las_directory: str = "Las data", train_ratio: float = 0.7):
        """
        Initialize multi-file scenario.

        Args:
            las_directory: Directory containing LAS files
            train_ratio: Fraction of files to use for training
        """
        self.las_directory = las_directory
        self.train_ratio = train_ratio
        self.loader = LASDataLoader(las_directory)
        self.formatter = WellLogFormatter()

    def run_scenario(self) -> Dict:
        """
        Run the multi-file scenario.

        Returns:
            Dictionary containing results and metrics
        """
        print("="*60)
        print("MULTI-FILE SCENARIO")
        print("="*60)
        print("Training and prediction on different LAS files")
        print()

        # Split files into training and prediction sets
        train_files, pred_files = self.loader.split_files_for_scenarios(self.train_ratio)

        print(f"Training files: {[os.path.basename(f) for f in train_files]}")
        print(f"Prediction files: {[os.path.basename(f) for f in pred_files]}")
        print()

        # Load training data
        train_data = self._load_files(train_files, "training")

        # Load prediction data
        pred_data = self._load_files(pred_files, "prediction")

        # Prepare data for TFT
        train_processed, valid_processed, test_processed = self._prepare_tft_data(train_data, pred_data)

        # Train model
        model, training_results = self._train_model(train_processed, valid_processed)

        # Make predictions on the separate prediction files
        predictions = self._make_predictions(model, test_processed)

        # Evaluate results
        metrics = self._evaluate_predictions(predictions, test_processed)

        # Create visualizations
        self._create_visualizations(train_data, pred_data, predictions, train_files, pred_files)

        results = {
            'scenario': 'multi_file',
            'train_files': train_files,
            'pred_files': pred_files,
            'data_summary': self._get_data_summary(train_data, pred_data),
            'training_results': training_results,
            'predictions': predictions,
            'metrics': metrics
        }

        self._print_results_summary(results)

        return results

    def _load_files(self, file_list: List[str], purpose: str) -> pd.DataFrame:
        """Load multiple LAS files and combine them."""
        print(f"Loading {len(file_list)} files for {purpose}...")

        all_data = []
        for file_path in file_list:
            df = self.loader.load_single_las(file_path)
            if df is not None and len(df) > 0:
                all_data.append(df)

        if not all_data:
            raise ValueError(f"No valid data could be loaded for {purpose}")

        combined_df = pd.concat(all_data, ignore_index=True)
        print(f"Loaded {len(combined_df)} samples from {combined_df['well_id'].nunique()} wells for {purpose}")

        return combined_df

    def _prepare_tft_data(self, train_data: pd.DataFrame, pred_data: pd.DataFrame) -> Tuple[pd.DataFrame, pd.DataFrame, pd.DataFrame]:
        """Prepare data for TFT training."""
        print("\nPreparing data for TFT...")

        # For multi-file scenario, we use training files for train/valid split
        # and prediction files as test data

        # Split training data into train and validation
        train_wells = train_data['well_id'].unique()
        np.random.seed(42)
        np.random.shuffle(train_wells)

        split_idx = int(len(train_wells) * 0.8)  # 80% for training, 20% for validation
        train_well_ids = train_wells[:split_idx]
        valid_well_ids = train_wells[split_idx:]

        train_df = train_data[train_data['well_id'].isin(train_well_ids)].copy()
        valid_df = train_data[train_data['well_id'].isin(valid_well_ids)].copy()
        test_df = pred_data.copy()  # Use prediction files as test data

        print(f"Train wells: {list(train_well_ids)}")
        print(f"Valid wells: {list(valid_well_ids)}")
        print(f"Test wells: {list(test_df['well_id'].unique())}")

        # Set scalers using training data only
        self.formatter.set_scalers(train_df)

        # Transform all datasets
        train_transformed = self.formatter.transform_inputs(train_df)
        valid_transformed = self.formatter.transform_inputs(valid_df)
        test_transformed = self.formatter.transform_inputs(test_df)

        return train_transformed, valid_transformed, test_transformed

    def _train_model(self, train_data: pd.DataFrame, valid_data: pd.DataFrame) -> Tuple[ModelClass, Dict]:
        """Train the TFT model."""
        print("\nTraining TFT model...")

        # Get model parameters
        fixed_params = self.formatter.get_fixed_params()
        model_params = self.formatter.get_default_model_params()

        # Reduce parameters for testing
        fixed_params['num_epochs'] = 10  # Reduced for testing
        fixed_params['total_time_steps'] = 20
        fixed_params['num_encoder_steps'] = 15
        model_params['hidden_layer_size'] = 32
        model_params['minibatch_size'] = 16

        # Combine parameters
        all_params = {**fixed_params, **model_params}
        all_params['column_definition'] = self.formatter.get_column_definition()
        all_params['model_folder'] = 'temp_model_multi'

        # Create model
        model = ModelClass(all_params, use_cudnn=False)

        # Cache data
        train_samples, valid_samples = 500, 100  # Reduced for testing
        model.cache_batched_data(train_data, "train", num_samples=train_samples)
        model.cache_batched_data(valid_data, "valid", num_samples=valid_samples)

        # Train model
        model.fit()

        training_results = {
            'epochs_completed': fixed_params['num_epochs'],
            'train_samples': len(train_data),
            'valid_samples': len(valid_data),
            'train_wells': train_data['well_id'].nunique(),
            'valid_wells': valid_data['well_id'].nunique()
        }

        return model, training_results

    def _make_predictions(self, model: ModelClass, test_data: pd.DataFrame) -> Dict:
        """Make predictions using the trained model."""
        print("\nMaking predictions...")

        # Get predictions
        output_map = model.predict(test_data, return_targets=True)

        # Format predictions
        targets = self.formatter.format_predictions(output_map["targets"])
        p50_forecast = self.formatter.format_predictions(output_map["p50"])
        p90_forecast = self.formatter.format_predictions(output_map["p90"])
        p10_forecast = self.formatter.format_predictions(output_map["p10"])

        predictions = {
            'targets': targets,
            'p50': p50_forecast,
            'p90': p90_forecast,
            'p10': p10_forecast,
            'test_samples': len(test_data)
        }

        return predictions

    def _evaluate_predictions(self, predictions: Dict, test_data: pd.DataFrame) -> Dict:
        """Evaluate prediction quality."""
        print("\nEvaluating predictions...")

        targets = predictions['targets']
        p50_forecast = predictions['p50']

        # Extract numerical data (remove metadata columns)
        def extract_numerical_data(data):
            return data[[col for col in data.columns
                        if col not in {"forecast_time", "identifier"}]]

        targets_num = extract_numerical_data(targets)
        p50_num = extract_numerical_data(p50_forecast)

        # Calculate metrics
        mae = np.mean(np.abs(targets_num.values - p50_num.values))
        rmse = np.sqrt(np.mean((targets_num.values - p50_num.values) ** 2))

        # Calculate normalized quantile loss
        p50_loss = utils.numpy_normalised_quantile_loss(
            targets_num.values, p50_num.values, 0.5)

        metrics = {
            'mae': mae,
            'rmse': rmse,
            'p50_loss': p50_loss,
            'target_mean': np.mean(targets_num.values),
            'target_std': np.std(targets_num.values)
        }

        return metrics

    def _create_visualizations(self, train_data: pd.DataFrame, pred_data: pd.DataFrame,
                             predictions: Dict, train_files: List[str], pred_files: List[str]):
        """Create visualization plots for multi-file scenario."""
        print("\nCreating visualizations...")

        try:
            # Create figure with subplots
            fig, axes = plt.subplots(2, 3, figsize=(18, 10))
            fig.suptitle('Multi-File Scenario Results', fontsize=16)

            # Plot 1: Data distribution by wells
            train_counts = train_data['well_id'].value_counts()
            pred_counts = pred_data['well_id'].value_counts()

            axes[0, 0].bar(range(len(train_counts)), train_counts.values, alpha=0.7, label='Training')
            axes[0, 0].set_title('Training Data Distribution by Well')
            axes[0, 0].set_xlabel('Well Index')
            axes[0, 0].set_ylabel('Number of Samples')
            axes[0, 0].tick_params(axis='x', rotation=45)

            axes[0, 1].bar(range(len(pred_counts)), pred_counts.values, alpha=0.7, label='Prediction', color='orange')
            axes[0, 1].set_title('Prediction Data Distribution by Well')
            axes[0, 1].set_xlabel('Well Index')
            axes[0, 1].set_ylabel('Number of Samples')
            axes[0, 1].tick_params(axis='x', rotation=45)

            # Plot 2: P-wave velocity distributions
            axes[0, 2].hist(train_data['p_wave'].dropna(), bins=30, alpha=0.7, label='Training', density=True)
            axes[0, 2].hist(pred_data['p_wave'].dropna(), bins=30, alpha=0.7, label='Prediction', density=True)
            axes[0, 2].set_xlabel('P-wave Velocity (m/s)')
            axes[0, 2].set_ylabel('Density')
            axes[0, 2].set_title('P-wave Velocity Distributions')
            axes[0, 2].legend()

            # Plot 3: Prediction vs actual (if we have predictions)
            if predictions and len(predictions['targets']) > 0:
                targets = predictions['targets'].iloc[:, 2:].values.flatten()  # Skip metadata columns
                p50_pred = predictions['p50'].iloc[:, 2:].values.flatten()

                axes[1, 0].scatter(targets, p50_pred, alpha=0.6, s=10)
                axes[1, 0].plot([targets.min(), targets.max()],
                               [targets.min(), targets.max()], 'r--', lw=2)
                axes[1, 0].set_xlabel('Actual P-wave Velocity')
                axes[1, 0].set_ylabel('Predicted P-wave Velocity')
                axes[1, 0].set_title('Predictions vs Actual')

            # Plot 4: Feature comparison between train and prediction data
            feature_cols = ['gamma_ray', 'resistivity', 'density', 'neutron']
            available_cols = [col for col in feature_cols if col in train_data.columns and col in pred_data.columns]

            if available_cols:
                train_means = train_data[available_cols].mean()
                pred_means = pred_data[available_cols].mean()

                x = np.arange(len(available_cols))
                width = 0.35

                axes[1, 1].bar(x - width/2, train_means, width, label='Training', alpha=0.7)
                axes[1, 1].bar(x + width/2, pred_means, width, label='Prediction', alpha=0.7)
                axes[1, 1].set_xlabel('Features')
                axes[1, 1].set_ylabel('Mean Values')
                axes[1, 1].set_title('Feature Means Comparison')
                axes[1, 1].set_xticks(x)
                axes[1, 1].set_xticklabels(available_cols, rotation=45)
                axes[1, 1].legend()

            # Plot 5: Depth ranges comparison
            train_depth_ranges = train_data.groupby('well_id')['depth'].agg(['min', 'max'])
            pred_depth_ranges = pred_data.groupby('well_id')['depth'].agg(['min', 'max'])

            axes[1, 2].scatter(train_depth_ranges['min'], train_depth_ranges['max'],
                              label='Training Wells', alpha=0.7, s=50)
            axes[1, 2].scatter(pred_depth_ranges['min'], pred_depth_ranges['max'],
                              label='Prediction Wells', alpha=0.7, s=50)
            axes[1, 2].set_xlabel('Min Depth (m)')
            axes[1, 2].set_ylabel('Max Depth (m)')
            axes[1, 2].set_title('Depth Ranges by Well')
            axes[1, 2].legend()

            plt.tight_layout()

            # Save plot
            plot_filename = "multi_file_results.png"
            plt.savefig(plot_filename, dpi=150, bbox_inches='tight')
            print(f"Visualization saved as: {plot_filename}")

            plt.show()

        except Exception as e:
            print(f"Error creating visualizations: {str(e)}")

    def _get_data_summary(self, train_data: pd.DataFrame, pred_data: pd.DataFrame) -> Dict:
        """Get summary statistics for the data."""
        return {
            'train_samples': len(train_data),
            'pred_samples': len(pred_data),
            'train_wells': train_data['well_id'].nunique(),
            'pred_wells': pred_data['well_id'].nunique(),
            'train_well_ids': list(train_data['well_id'].unique()),
            'pred_well_ids': list(pred_data['well_id'].unique()),
            'train_depth_range': (train_data['depth'].min(), train_data['depth'].max()),
            'pred_depth_range': (pred_data['depth'].min(), pred_data['depth'].max())
        }

    def _print_results_summary(self, results: Dict):
        """Print a summary of the results."""
        print("\n" + "="*60)
        print("MULTI-FILE SCENARIO RESULTS SUMMARY")
        print("="*60)

        data_summary = results['data_summary']
        metrics = results['metrics']
        training_results = results['training_results']

        print(f"Training files: {[os.path.basename(f) for f in results['train_files']]}")
        print(f"Prediction files: {[os.path.basename(f) for f in results['pred_files']]}")
        print()
        print("Data summary:")
        print(f"  Training wells: {data_summary['train_wells']} ({data_summary['train_samples']} samples)")
        print(f"  Prediction wells: {data_summary['pred_wells']} ({data_summary['pred_samples']} samples)")
        print(f"  Training depth range: {data_summary['train_depth_range'][0]:.1f} - {data_summary['train_depth_range'][1]:.1f} m")
        print(f"  Prediction depth range: {data_summary['pred_depth_range'][0]:.1f} - {data_summary['pred_depth_range'][1]:.1f} m")
        print()
        print("Training summary:")
        print(f"  Epochs completed: {training_results['epochs_completed']}")
        print(f"  Training wells used: {training_results['train_wells']}")
        print(f"  Validation wells used: {training_results['valid_wells']}")
        print()
        print("Prediction metrics:")
        print(f"  MAE: {metrics['mae']:.2f}")
        print(f"  RMSE: {metrics['rmse']:.2f}")
        print(f"  P50 Loss: {metrics['p50_loss']:.4f}")
        print(f"  Target mean: {metrics['target_mean']:.2f}")
        print(f"  Target std: {metrics['target_std']:.2f}")
        print("="*60)

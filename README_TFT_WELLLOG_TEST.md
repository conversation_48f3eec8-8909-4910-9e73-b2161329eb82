# TFT Well Log Test Suite

This comprehensive test suite demonstrates training and prediction using the Temporal Fusion Transformer (TFT) implementation with LAS (Log ASCII Standard) files for well log data analysis.

## Overview

The test suite implements two distinct testing scenarios:

1. **Single-file scenario**: Training and prediction on the same LAS file, split by depth intervals
2. **Multi-file scenario**: Training and prediction on different LAS files (some wells for training, others for prediction)

The model predicts **P-wave velocity** using the following input features:
- Gamma Ray (GR)
- Resistivity (RT) 
- Density (RHOB)
- Neutron Porosity (NPHI)

## Files Structure

```
├── test_tft_welllog_comprehensive.py  # Main test orchestrator
├── test_scenarios.py                  # Individual test scenario implementations
├── las_utils.py                       # LAS file loading and processing utilities
├── data_formatters/welllog.py         # Custom data formatter for well log data
├── Las data/                          # Directory containing LAS files
│   ├── B-G-10_RP_INPUT.las
│   ├── B-G-6_RP_INPUT.las
│   ├── B-L-15_RP_INPUT.las
│   ├── B-L-1_RP_INPUT.las
│   ├── B-L-6_RP_INPUT.las
│   ├── B-L-9_RP_INPUT.las
│   └── EB-1_RP_INPUT.las
└── test_results/                      # Output directory for results
```

## Dependencies

The test suite requires the following packages:
- `lasio` - For reading LAS files
- `tensorflow` - TFT model implementation
- `pandas` - Data manipulation
- `numpy` - Numerical computations
- `matplotlib` - Visualization
- `seaborn` - Statistical plotting
- `scikit-learn` - Data preprocessing

Install missing dependencies:
```bash
pip install lasio
```

## Usage

### Basic Usage

Run both scenarios with default settings:
```bash
python test_tft_welllog_comprehensive.py
```

### Advanced Usage

Run specific scenarios:
```bash
# Run only single-file scenario
python test_tft_welllog_comprehensive.py --scenarios single

# Run only multi-file scenario
python test_tft_welllog_comprehensive.py --scenarios multi

# Run both scenarios
python test_tft_welllog_comprehensive.py --scenarios single multi
```

Specify custom directories:
```bash
python test_tft_welllog_comprehensive.py --las-dir "path/to/las/files" --output-dir "custom_results"
```

Use a specific file for single-file scenario:
```bash
python test_tft_welllog_comprehensive.py --scenarios single --target-file "Las data/B-G-10_RP_INPUT.las"
```

Enable verbose output:
```bash
python test_tft_welllog_comprehensive.py --verbose
```

### Command Line Options

- `--las-dir`: Directory containing LAS files (default: "Las data")
- `--output-dir`: Output directory for results (default: "test_results")
- `--scenarios`: Scenarios to run (choices: single, multi; default: both)
- `--target-file`: Specific LAS file for single-file scenario
- `--verbose`: Enable verbose output

## Test Scenarios

### Single-File Scenario

**Methodology:**
1. Loads a single LAS file
2. Sorts data by depth (temporal ordering)
3. Splits data by depth intervals:
   - 70% for training (shallow depths)
   - 15% for validation (middle depths)
   - 15% for testing (deep depths)
4. Trains TFT model on training data
5. Makes predictions on test data
6. Evaluates performance and creates visualizations

**Key Features:**
- Temporal/depth-based splitting preserves geological continuity
- Tests model's ability to extrapolate to deeper formations
- Provides baseline performance on single-well data

### Multi-File Scenario

**Methodology:**
1. Loads multiple LAS files from different wells
2. Splits files into training and prediction sets (70/30 split)
3. Uses training files for model training and validation
4. Uses prediction files as independent test data
5. Evaluates cross-well prediction performance

**Key Features:**
- Tests model's generalization across different wells
- Simulates real-world scenario of predicting on new wells
- Evaluates spatial transferability of learned patterns

## Data Processing Pipeline

### LAS File Loading
1. **File Discovery**: Automatically finds all .las files in specified directory
2. **Curve Mapping**: Maps various curve naming conventions to standard names
3. **Data Extraction**: Extracts required curves (depth, P-wave, GR, RT, RHOB, NPHI)
4. **Unit Conversion**: Converts P-wave slowness (DT) to velocity if needed
5. **Quality Control**: Handles missing values (-999.25) and removes outliers

### Data Preprocessing
1. **Null Value Handling**: Replaces LAS null values with NaN
2. **Data Cleaning**: Removes rows with insufficient valid measurements
3. **Outlier Removal**: Clips extreme values beyond 3 standard deviations
4. **Feature Engineering**: Adds normalized depth as additional input
5. **Scaling**: Applies StandardScaler to numerical features

### TFT Model Configuration
- **Time Steps**: 20 total (15 encoder + 5 decoder)
- **Hidden Size**: 32 units (reduced for well log data)
- **Batch Size**: 16 samples
- **Learning Rate**: 0.001
- **Epochs**: 10 (reduced for testing)
- **Dropout**: 0.1

## Output and Results

### Generated Files
- `single_file_results.json`: Detailed single-file scenario results
- `multi_file_results.json`: Detailed multi-file scenario results
- `comprehensive_report.txt`: Summary report of all scenarios
- `single_file_results_*.png`: Visualization plots for single-file scenario
- `multi_file_results.png`: Visualization plots for multi-file scenario

### Evaluation Metrics
- **MAE**: Mean Absolute Error
- **RMSE**: Root Mean Square Error
- **P50 Loss**: Normalized quantile loss at 50th percentile
- **Target Statistics**: Mean and standard deviation of actual values

### Visualizations
- Data distribution plots
- P-wave velocity vs depth relationships
- Prediction vs actual scatter plots
- Feature correlation matrices
- Cross-well comparison plots

## Interpretation Guidelines

### Good Performance Indicators
- MAE < 10% of target standard deviation
- RMSE < 15% of target standard deviation
- P50 Loss < 0.1
- Strong correlation in prediction vs actual plots

### Common Issues
- **High prediction errors**: May indicate insufficient training data or poor feature quality
- **Systematic bias**: Could suggest need for better normalization or feature engineering
- **Poor cross-well performance**: May indicate overfitting to specific well characteristics

## Customization

### Adding New Features
1. Modify `curve_mappings` in `las_utils.py` to include new curves
2. Update `_column_definition` in `data_formatters/welllog.py`
3. Adjust data preprocessing pipeline as needed

### Changing Model Parameters
Modify parameters in `WellLogFormatter.get_fixed_params()` and `get_default_model_params()`:
- Increase `total_time_steps` for longer sequences
- Adjust `hidden_layer_size` for model complexity
- Modify `num_epochs` for training duration

### Custom Data Splits
Override splitting logic in scenario classes:
- Modify `_split_by_depth()` for single-file scenario
- Adjust `split_files_for_scenarios()` for multi-file scenario

## Troubleshooting

### Common Errors
1. **"No LAS files found"**: Check LAS directory path and file extensions
2. **"Could not find required curves"**: Verify LAS files contain necessary well log data
3. **"Insufficient training data"**: Increase minimum samples or reduce time steps
4. **TensorFlow errors**: Ensure TF 2.x with v1 compatibility is properly configured

### Performance Issues
- Reduce batch size if running out of memory
- Decrease model complexity (hidden_layer_size) for faster training
- Use fewer epochs for quicker testing

## Example Output

```
================================================================================
TFT WELL LOG COMPREHENSIVE TEST SUITE
================================================================================
Test started at: 2025-01-XX XX:XX:XX
LAS directory: Las data
Output directory: test_results

Found 7 LAS files in Las data
✓ Successfully validated LAS file format
✓ All required curves found
Setup validation completed successfully.

============================================================
SINGLE-FILE SCENARIO
============================================================
Training and prediction on the same LAS file, split by depth intervals

Using LAS file: B-G-10_RP_INPUT.las
Loaded 8674 samples from B-G-10

Splitting data by depth (train ratio: 0.7)
Train samples: 6071
Valid samples: 1301
Test samples: 1302

Training TFT model...
Making predictions...
Evaluating predictions...
Creating visualizations...

============================================================
MULTI-FILE SCENARIO
============================================================
Training and prediction on different LAS files

Multi-file split: 5 training files, 2 prediction files
Training files: ['B-G-10_RP_INPUT.las', 'B-G-6_RP_INPUT.las', ...]
Prediction files: ['B-L-15_RP_INPUT.las', 'EB-1_RP_INPUT.las']

Training TFT model...
Making predictions...
Evaluating predictions...
Creating visualizations...

================================================================================
COMPREHENSIVE TEST RESULTS SUMMARY
================================================================================

Single-File Scenario Summary:
----------------------------------------
  MAE: 156.234
  RMSE: 203.567
  P50 Loss: 0.0823

Multi-File Scenario Summary:
----------------------------------------
  MAE: 189.456
  RMSE: 245.123
  P50 Loss: 0.1034

Scenario Comparison:
----------------------------------------
  MAE: Single=156.234, Multi=189.456 (+21.3%)
  RMSE: Single=203.567, Multi=245.123 (+20.4%)
  P50_LOSS: Single=0.082, Multi=0.103 (+25.6%)

✓ Comprehensive test completed successfully!
```

This test suite provides a robust framework for evaluating TFT performance on well log data and can be easily extended for additional scenarios or different prediction targets.

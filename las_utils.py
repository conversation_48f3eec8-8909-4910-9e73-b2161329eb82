"""
Utility functions for loading and processing LAS (Log ASCII Standard) files
for use with the Temporal Fusion Transformer (TFT) model.

This module provides functions to:
1. Load LAS files using the lasio library
2. Extract relevant well log curves (gamma ray, resistivity, density, neutron, P-wave)
3. Handle missing values and data cleaning
4. Convert to pandas DataFrame format suitable for TFT training
"""

import os
import glob
import pandas as pd
import numpy as np
import lasio
from typing import List, Dict, Optional, Tuple
import warnings

# Suppress lasio warnings for cleaner output
warnings.filterwarnings('ignore', category=UserWarning, module='lasio')


class LASDataLoader:
    """Class for loading and processing LAS files for TFT training."""
    
    def __init__(self, las_directory: str = "Las data"):
        """
        Initialize the LAS data loader.
        
        Args:
            las_directory: Directory containing LAS files
        """
        self.las_directory = las_directory
        self.null_value = -999.25  # Standard LAS null value
        
        # Define curve mappings - maps standard names to possible LAS curve names
        self.curve_mappings = {
            'gamma_ray': ['GR', 'GAMMA_RAY', 'GAMMA', 'SGR'],
            'resistivity': ['RT', 'RES', 'RESISTIVITY', 'ILD', 'LLD'],
            'density': ['RHOB', 'DENS', 'DENSITY', 'RHOZ'],
            'neutron': ['NPHI', 'NEUT', 'NEUTRON', 'PHIN'],
            'p_wave': ['P-WAVE', 'PWAVE', 'VP', 'PVEL', 'DT'],  # DT is slowness, will convert
            'depth': ['DEPTH', 'DEPT', 'MD', 'TVDSS']
        }
    
    def get_las_files(self) -> List[str]:
        """
        Get list of all LAS files in the directory.
        
        Returns:
            List of LAS file paths
        """
        pattern = os.path.join(self.las_directory, "*.las")
        las_files = glob.glob(pattern)
        print(f"Found {len(las_files)} LAS files in {self.las_directory}")
        return las_files
    
    def load_single_las(self, file_path: str) -> Optional[pd.DataFrame]:
        """
        Load a single LAS file and extract relevant curves.
        
        Args:
            file_path: Path to the LAS file
            
        Returns:
            DataFrame with processed well log data, or None if loading fails
        """
        try:
            print(f"Loading LAS file: {os.path.basename(file_path)}")
            
            # Load LAS file
            las = lasio.read(file_path)
            
            # Extract well name from file or header
            well_name = self._extract_well_name(las, file_path)
            
            # Convert to DataFrame
            df = las.df()
            df.reset_index(inplace=True)  # Move depth from index to column
            
            # Standardize curve names and extract required curves
            df_processed = self._extract_and_standardize_curves(df, well_name)
            
            if df_processed is not None:
                print(f"Successfully processed {len(df_processed)} samples from {well_name}")
                return df_processed
            else:
                print(f"Failed to extract required curves from {well_name}")
                return None
                
        except Exception as e:
            print(f"Error loading {file_path}: {str(e)}")
            return None
    
    def _extract_well_name(self, las: lasio.LASFile, file_path: str) -> str:
        """Extract well name from LAS header or filename."""
        # Try to get well name from LAS header
        if hasattr(las.well, 'WELL') and las.well.WELL.value:
            return str(las.well.WELL.value).strip()
        
        # Fall back to filename
        return os.path.splitext(os.path.basename(file_path))[0]
    
    def _extract_and_standardize_curves(self, df: pd.DataFrame, well_name: str) -> Optional[pd.DataFrame]:
        """
        Extract and standardize curve names from the DataFrame.
        
        Args:
            df: Raw DataFrame from LAS file
            well_name: Name of the well
            
        Returns:
            Processed DataFrame with standardized column names
        """
        # Create output DataFrame
        output_data = {'well_id': well_name}
        
        # Find and extract each required curve
        for standard_name, possible_names in self.curve_mappings.items():
            curve_data = self._find_curve(df, possible_names)
            
            if curve_data is not None:
                # Handle special case for P-wave velocity
                if standard_name == 'p_wave':
                    curve_data = self._process_p_wave(curve_data, possible_names)
                
                output_data[standard_name] = curve_data
            else:
                print(f"Warning: Could not find {standard_name} curve in {well_name}")
                # For missing curves, we'll skip this well
                return None
        
        # Create DataFrame
        result_df = pd.DataFrame(output_data)
        
        # Clean data
        result_df = self._clean_data(result_df)
        
        # Add derived features
        result_df = self._add_derived_features(result_df)
        
        return result_df
    
    def _find_curve(self, df: pd.DataFrame, possible_names: List[str]) -> Optional[np.ndarray]:
        """Find a curve in the DataFrame using possible names."""
        df_columns_upper = [col.upper() for col in df.columns]
        
        for name in possible_names:
            name_upper = name.upper()
            if name_upper in df_columns_upper:
                # Find the actual column name (preserving case)
                actual_col = df.columns[df_columns_upper.index(name_upper)]
                return df[actual_col].values
        
        return None
    
    def _process_p_wave(self, curve_data: np.ndarray, possible_names: List[str]) -> np.ndarray:
        """
        Process P-wave data, converting from slowness (DT) to velocity if needed.
        
        Args:
            curve_data: Raw curve data
            possible_names: List of possible curve names that were checked
            
        Returns:
            P-wave velocity in m/s
        """
        # Check if this might be slowness (DT) data
        # DT is typically in microseconds/foot, values usually > 40
        if np.nanmean(curve_data[curve_data != self.null_value]) > 40:
            # Convert from slowness (us/ft) to velocity (m/s)
            # Formula: V = 304.8 / DT (where 304.8 converts ft/s to m/s)
            velocity = np.where(
                curve_data != self.null_value,
                304.8 / curve_data,
                self.null_value
            )
            print("Converted slowness (DT) to P-wave velocity")
            return velocity
        else:
            # Assume it's already velocity
            return curve_data
    
    def _clean_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Clean the data by handling null values and outliers.
        
        Args:
            df: Input DataFrame
            
        Returns:
            Cleaned DataFrame
        """
        # Replace null values with NaN
        numeric_columns = df.select_dtypes(include=[np.number]).columns
        for col in numeric_columns:
            df[col] = df[col].replace(self.null_value, np.nan)
        
        # Remove rows where all log values are NaN
        log_columns = ['gamma_ray', 'resistivity', 'density', 'neutron', 'p_wave']
        available_log_columns = [col for col in log_columns if col in df.columns]
        
        # Keep rows that have at least 3 out of 5 log values
        df['valid_count'] = df[available_log_columns].notna().sum(axis=1)
        df = df[df['valid_count'] >= 3].copy()
        df.drop('valid_count', axis=1, inplace=True)
        
        # Remove extreme outliers (beyond 3 standard deviations)
        for col in available_log_columns:
            if col in df.columns:
                mean_val = df[col].mean()
                std_val = df[col].std()
                if not np.isnan(std_val) and std_val > 0:
                    lower_bound = mean_val - 3 * std_val
                    upper_bound = mean_val + 3 * std_val
                    df[col] = df[col].clip(lower_bound, upper_bound)
        
        return df
    
    def _add_derived_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Add derived features for TFT training.
        
        Args:
            df: Input DataFrame
            
        Returns:
            DataFrame with additional features
        """
        # Normalize depth within each well (0 to 1)
        if 'depth' in df.columns:
            min_depth = df['depth'].min()
            max_depth = df['depth'].max()
            if max_depth > min_depth:
                df['depth_normalized'] = (df['depth'] - min_depth) / (max_depth - min_depth)
            else:
                df['depth_normalized'] = 0.5  # If all depths are the same
        
        return df
    
    def load_all_las_files(self) -> pd.DataFrame:
        """
        Load all LAS files in the directory and combine into a single DataFrame.
        
        Returns:
            Combined DataFrame with all well log data
        """
        las_files = self.get_las_files()
        
        if not las_files:
            raise ValueError(f"No LAS files found in {self.las_directory}")
        
        all_data = []
        
        for file_path in las_files:
            df = self.load_single_las(file_path)
            if df is not None and len(df) > 0:
                all_data.append(df)
        
        if not all_data:
            raise ValueError("No valid data could be extracted from LAS files")
        
        # Combine all data
        combined_df = pd.concat(all_data, ignore_index=True)
        
        print(f"\nCombined data summary:")
        print(f"Total samples: {len(combined_df)}")
        print(f"Number of wells: {combined_df['well_id'].nunique()}")
        print(f"Wells: {list(combined_df['well_id'].unique())}")
        
        # Print data quality summary
        self._print_data_summary(combined_df)
        
        return combined_df
    
    def _print_data_summary(self, df: pd.DataFrame):
        """Print summary statistics for the loaded data."""
        print(f"\nData quality summary:")
        log_columns = ['gamma_ray', 'resistivity', 'density', 'neutron', 'p_wave']
        
        for col in log_columns:
            if col in df.columns:
                valid_count = df[col].notna().sum()
                total_count = len(df)
                completeness = (valid_count / total_count) * 100
                print(f"{col}: {completeness:.1f}% complete ({valid_count}/{total_count})")
    
    def split_files_for_scenarios(self, train_ratio: float = 0.7) -> Tuple[List[str], List[str]]:
        """
        Split LAS files into training and prediction sets for multi-file scenario.
        
        Args:
            train_ratio: Fraction of files to use for training
            
        Returns:
            Tuple of (training_files, prediction_files)
        """
        las_files = self.get_las_files()
        
        if len(las_files) < 2:
            raise ValueError("Need at least 2 LAS files for multi-file scenario")
        
        # Shuffle files for random split
        np.random.seed(42)  # For reproducibility
        shuffled_files = np.random.permutation(las_files)
        
        split_idx = int(len(shuffled_files) * train_ratio)
        train_files = shuffled_files[:split_idx].tolist()
        pred_files = shuffled_files[split_idx:].tolist()
        
        print(f"Multi-file split: {len(train_files)} training files, {len(pred_files)} prediction files")
        
        return train_files, pred_files
